import { Metadata } from "next"
import { notFound } from "next/navigation"
import { getSalonInfoFromSlug } from "@/lib/utils/salon-resolver"
import { supabaseClient } from "@/lib/supabase-singleton"
import SalonLandingPageClient from "./salon-landing-client"

interface PageProps {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params

  try {
    // Salon bilgilerini al
    const salonInfo = await getSalonInfoFromSlug(slug, supabaseClient)

    if (!salonInfo) {
      return {
        title: "Salon Bulunamadı | SalonFlow",
        description: "Aradığınız salon bulunamadı."
      }
    }

    // Salon detaylarını al
    const { data: salon, error } = await supabaseClient
      .from('salons')
      .select('name, address, phone, email')
      .eq('id', salonInfo.salonId)
      .single()

    if (error || !salon) {
      return {
        title: "Salon Bulunamadı | SalonFlow",
        description: "Aradığınız salon bulunamadı."
      }
    }

    const title = `${salon.name} - Profesyonel Berber Hizmetleri | SalonFlow`
    const description = `${salon.name} ile profesyonel berber hizmetleri. Saç kesimi, sakal tıraşı ve daha fazlası için randevu almak hemen tıklayın. ${salon.address ? `Adres: ${salon.address}` : ''}`

    return {
      title,
      description,
      keywords: `${salon.name}, berber, kuaför, saç kesimi, sakal tıraşı, randevu, ${salon.address ? salon.address.split(' ').slice(-2).join(' ') : ''}`,
      openGraph: {
        title,
        description,
        type: 'website',
        locale: 'tr_TR',
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
      },
      robots: {
        index: true,
        follow: true,
      }
    }
  } catch (error) {
    console.error("Metadata oluşturulurken hata:", error)
    return {
      title: "SalonFlow - Profesyonel Berber Hizmetleri",
      description: "Profesyonel berber hizmetleri için randevu alın."
    }
  }
}

interface Service {
  id: string
  name: string
  description?: string
  duration: number
  price: number
}

// Optimize edilmiş animasyon varyantları
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94] // easeOutQuart
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.2,
      ease: [0.55, 0.06, 0.68, 0.19] // easeInQuart
    }
  }
}

// Reduced motion için basit varyant
const reducedMotionVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.2 } },
  exit: { opacity: 0, transition: { duration: 0.1 } }
}

// Loading spinner component - memoized
const LoadingSpinner = () => (
    <div className="flex justify-center items-center h-screen">
      <div className="flex flex-col items-center gap-4">
        {/* will-change-transform buradan kaldırıldı */}
        <div className="h-12 w-12 rounded-full border-4 border-primary border-t-transparent animate-spin"></div>
      </div>
    </div>
)

// Error state component - memoized
const ErrorState = () => (
    <div className="flex flex-col items-center justify-center h-64 gap-4">
      <h1 className="text-2xl font-bold">Salon Bulunamadı</h1>
      <p className="text-muted-foreground">
        Aradığınız salon bulunamadı veya artık mevcut değil.
      </p>
      <Button asChild size="lg">
        <Link href="/">Ana Sayfaya Dön</Link>
      </Button>
    </div>
)

export default function SalonLandingPage() {
  const params = useParams()
  const slug = params.slug as string
  const [services, setServices] = useState<Service[]>([])
  const [servicesLoading, setServicesLoading] = useState(true)
  const shouldReduceMotion = useReducedMotion()

  // SalonContext'ten bilgileri al
  const { salon, salonId, isLoading, dataLoaded } = useSalon()

  // Memoized değerler
  const isInitialLoading = useMemo(() => isLoading && !dataLoaded, [isLoading, dataLoaded])
  const hasSalonData = useMemo(() => salon && salonId, [salon, salonId])

  // Optimized service loading with useCallback
  const loadServices = useCallback(async () => {
    if (!salonId) return

    try {
      setServicesLoading(true)
      const servicesData = await publicAccess.getPublicServicesBySalonId(salonId)
      setServices(servicesData || [])
    } catch (error) {
      console.error("Error loading services:", error)
      setServices([])
    } finally {
      setServicesLoading(false)
    }
  }, [salonId])

  // Load services effect
  useEffect(() => {
    loadServices()
  }, [loadServices])

  // Motion variants seçimi
  const motionVariants = shouldReduceMotion ? reducedMotionVariants : pageVariants

  // Layout komponenti - header ve footer için
  const PageLayout = ({ children }: { children: React.ReactNode }) => (
      <div className="min-h-screen flex flex-col">
        <header className="border-b backdrop-blur-md bg-background/80 will-change-transform">
          <div className="container mx-auto flex justify-between items-center py-4">
          <span className="text-2xl font-bold tracking-tight">
          </span>
          </div>
        </header>
        <main className="flex-1 container mx-auto py-12">
          {children}
        </main>
      </div>
  )

  // Loading state
  if (isInitialLoading) {
    return (
        <LoadingSpinner />
    )
  }

  // Error state
  if (!hasSalonData) {
    return (
        <PageLayout>
          <ErrorState />
        </PageLayout>
    )
  }

  return (
      <BookingModalProvider salonId={salonId}>
        <div className="min-h-screen flex flex-col">
          <ClientHeader currentPage="home" />

          {/* Main Content */}
          <main className="flex-1">
            <AnimatePresence mode="wait">
              <motion.div
                  key={slug}
                  variants={motionVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  style={{ willChange: 'transform, opacity' }}
                  className="will-change-transform"
              >
                {/* Hero Section */}
                <HeroSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Services Section */}
                <ServicesSection
                    services={servicesLoading ? [] : services}
                    salonId={salonId}
                />

                {/* About Section */}
                <AboutSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Testimonials Section */}
                <TestimonialsSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Contact Section */}
                <ContactSection
                    salon={salon}
                    salonId={salonId}
                />
              </motion.div>
            </AnimatePresence>
          </main>

          {/* Footer - Optimize edilmiş */}
          <footer className="border-t bg-gradient-to-r from-muted/30 to-muted/20 py-12">
            <div className="container mx-auto px-4">
              <div className="max-w-6xl mx-auto">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Salon Info */}
                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-foreground">{salon.name}</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      Profesyonel berber hizmetleri ile tarzınızı yansıtın.
                      Kalite, güven ve memnuniyet bizim önceliğimiz.
                    </p>
                    {salon.address && (
                        <p className="text-sm text-muted-foreground">
                          📍 {salon.address}
                        </p>
                    )}
                  </div>

                  {/* Quick Links */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-foreground">Hızlı Erişim</h4>
                    <nav role="navigation" aria-label="Footer navigation">
                      <ul className="space-y-2">
                        <li>
                          <a
                              href="#services"
                              className="text-muted-foreground hover:text-primary transition-colors duration-200"
                          >
                            Hizmetlerimiz
                          </a>
                        </li>
                        <li>
                          <a
                              href="#about"
                              className="text-muted-foreground hover:text-primary transition-colors duration-200"
                          >
                            Hakkımızda
                          </a>
                        </li>
                        <li>
                          <a
                              href="#contact"
                              className="text-muted-foreground hover:text-primary transition-colors duration-200"
                          >
                            İletişim
                          </a>
                        </li>
                      </ul>
                    </nav>
                  </div>

                  {/* Contact Info */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-foreground">İletişim</h4>
                    <address className="space-y-2 not-italic">
                      {salon.phone && (
                          <p className="text-sm text-muted-foreground">
                            <a
                                href={`tel:${salon.phone}`}
                                className="hover:text-primary transition-colors duration-200"
                            >
                              📞 {salon.phone}
                            </a>
                          </p>
                      )}
                      {salon.email && (
                          <p className="text-sm text-muted-foreground">
                            <a
                                href={`mailto:${salon.email}`}
                                className="hover:text-primary transition-colors duration-200"
                            >
                              ✉️ {salon.email}
                            </a>
                          </p>
                      )}
                    </address>
                  </div>
                </div>

                <div className="border-t border-border/50 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
                  <p className="text-sm text-muted-foreground mb-4 md:mb-0">
                    © 2024 {salon.name}. Tüm hakları saklıdır.
                  </p>
                  <p className="text-xs text-muted-foreground">
                    ⚡ SalonFlow ile güçlendirilmiştir
                  </p>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </BookingModalProvider>
  )
}