"use client"

import { useEffect, useState, use<PERSON>emo, use<PERSON><PERSON>back } from "react"
import Link from "next/link"
import { motion, AnimatePresence, useReducedMotion } from "framer-motion"

import { But<PERSON> } from "@/components/ui/button"
import { ClientHeader } from "@/components/client/client-header"
import { HeroSection } from "@/components/customer/hero-section"
import { ServicesSection } from "@/components/customer/services-section"
import { AboutSection } from "@/components/customer/about-section"
import { TestimonialsSection } from "@/components/customer/testimonials-section"
import { ContactSection } from "@/components/customer/contact-section"
import { BookingModalProvider } from "@/contexts/BookingModalContext"
import { useSalon } from "@/contexts/SalonContext"
import { publicAccess } from "@/lib/db"
import { useParams } from "next/navigation"

interface Service {
  id: string
  name: string
  description?: string
  duration: number
  price: number
}

// Optimize edilmiş animasyon varyantları
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3,
      ease: [0.25, 0.46, 0.45, 0.94] // easeOutQuart
    }
  },
  exit: {
    opacity: 0,
    y: -20,
    transition: {
      duration: 0.2,
      ease: [0.55, 0.06, 0.68, 0.19] // easeInQuart
    }
  }
}

// Reduced motion için basit varyant
const reducedMotionVariants = {
  initial: { opacity: 0 },
  animate: { opacity: 1, transition: { duration: 0.2 } },
  exit: { opacity: 0, transition: { duration: 0.1 } }
}

// Loading Spinner Component
const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
  </div>
)

// Error State Component
const ErrorState = () => (
  <div className="min-h-screen flex flex-col items-center justify-center text-center">
    <h1 className="text-2xl font-bold mb-4">Salon Bulunamadı</h1>
    <p className="text-muted-foreground mb-6">
      Aradığınız salon bulunamadı veya mevcut değil.
    </p>
    <Button asChild>
      <Link href="/">Ana Sayfaya Dön</Link>
    </Button>
  </div>
)

export default function SalonLandingPageClient() {
  const params = useParams()
  const slug = params.slug as string
  const [services, setServices] = useState<Service[]>([])
  const [servicesLoading, setServicesLoading] = useState(true)
  const shouldReduceMotion = useReducedMotion()

  // SalonContext'ten bilgileri al
  const { salon, salonId, isLoading, dataLoaded } = useSalon()

  // Memoized değerler
  const isInitialLoading = useMemo(() => isLoading && !dataLoaded, [isLoading, dataLoaded])
  const hasSalonData = useMemo(() => salon && salonId, [salon, salonId])

  // Hizmetleri yükle
  const loadServices = useCallback(async () => {
    if (!salonId) return

    try {
      setServicesLoading(true)
      const data = await publicAccess.getPublicActiveServicesBySalonId(salonId)
      setServices(data)
    } catch (error) {
      console.error("Hizmetler yüklenirken hata:", error)
      setServices([])
    } finally {
      setServicesLoading(false)
    }
  }, [salonId])

  // Salon yüklendiğinde hizmetleri yükle
  useEffect(() => {
    if (hasSalonData) {
      loadServices()
    }
  }, [hasSalonData, loadServices])

  // Motion variants seçimi
  const motionVariants = shouldReduceMotion ? reducedMotionVariants : pageVariants

  // Layout komponenti - header ve footer için
  const PageLayout = ({ children }: { children: React.ReactNode }) => (
      <div className="min-h-screen flex flex-col">
        <header className="border-b backdrop-blur-md bg-background/80 will-change-transform">
          <div className="container mx-auto flex justify-between items-center py-4">
          <span className="text-2xl font-bold tracking-tight">
          </span>
          </div>
        </header>
        <main className="flex-1 container mx-auto py-12">
          {children}
        </main>
      </div>
  )

  // Loading state
  if (isInitialLoading) {
    return (
        <LoadingSpinner />
    )
  }

  // Error state
  if (!hasSalonData) {
    return (
        <PageLayout>
          <ErrorState />
        </PageLayout>
    )
  }

  return (
      <BookingModalProvider>
        <div className="min-h-screen flex flex-col">
          {/* Header */}
          <ClientHeader />

          {/* Main Content */}
          <main className="flex-1">
            <AnimatePresence mode="wait">
              <motion.div
                  key={slug}
                  variants={motionVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  style={{ willChange: 'transform, opacity' }}
                  className="will-change-transform"
              >
                {/* Hero Section */}
                <HeroSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Services Section */}
                <ServicesSection
                    services={servicesLoading ? [] : services}
                    salonId={salonId}
                />

                {/* About Section */}
                <AboutSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Testimonials Section */}
                <TestimonialsSection
                    salonName={salon.name}
                    salonId={salonId}
                />

                {/* Contact Section */}
                <ContactSection
                    salon={salon}
                    salonId={salonId}
                />
              </motion.div>
            </AnimatePresence>
          </main>
        </div>
      </BookingModalProvider>
  )
}
